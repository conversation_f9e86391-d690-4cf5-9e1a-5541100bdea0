<template>
  <div class="chat-container">
    <!-- 登录界面 -->
    <LoginForm
      v-if="!isConnected"
      v-model="username"
      @join-chat="joinChat"
    />

    <!-- 聊天界面 -->
    <div v-else class="chat-interface">
      <!-- 头部 -->
      <ChatHeader
        :current-user="currentUser"
        @disconnect="disconnect"
      />

      <div class="chat-content">
        <!-- 侧边栏 -->
        <ChatSidebar
          v-model:active-tab="activeTab"
          :online-users="onlineUsers"
          :available-groups="availableGroups"
          :user-groups="userGroups"
          :current-user="currentUser"
          :selected-user="selectedUser"
          :selected-group="selectedGroup"
          :chat-mode="chatMode"
          @select-user="selectUser"
          @select-group="selectGroup"
          @join-group="joinGroup"
          @leave-group="leaveGroup"
          @create-group="showCreateGroupModal = true"
        />

        <!-- 聊天区域 -->
        <ChatWindow
          :chat-mode="chatMode"
          :selected-user="selectedUser"
          :selected-group="selectedGroup"
          :current-user="currentUser"
          :messages="messages"
          :group-messages="groupMessages"
          :typing-users="typingUsers"
          :message-input="messageInput"
          @update:message-input="messageInput = $event"
          @send-message="sendMessage"
          @send-group-message="sendGroupMessage"
          @typing="handleTyping"
          ref="chatWindow"
        />
      </div>
    </div>

    <!-- 创建群组模态框 -->
    <CreateGroupModal
      v-if="showCreateGroupModal"
      v-model:group-name="newGroupName"
      v-model:group-description="newGroupDescription"
      @create="createGroup"
      @close="resetCreateGroupForm"
    />

    <!-- 连接状态指示器 -->
    <ConnectionStatus
      :status="connectionStatus"
      :status-text="connectionStatusText"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import LoginForm from './LoginForm.vue';
import ChatHeader from './ChatHeader.vue';
import ChatSidebar from './Sidebar/ChatSidebar.vue';
import ChatWindow from './ChatWindow/ChatWindow.vue';
import CreateGroupModal from './Modals/CreateGroupModal.vue';
import ConnectionStatus from './Common/ConnectionStatus.vue';

import { useChat } from '@/composables/useChat';
import { useGroups } from '@/composables/useGroups';

// 使用聊天相关的composables
const {
  isConnected,
  connectionStatus,
  connectionStatusText,
  username,
  currentUser,
  onlineUsers,
  selectedUser,
  selectedGroup,
  chatMode,
  activeTab,
  messageInput,
  messages,
  groupMessages,
  typingUsers,
  joinChat,
  selectUser,
  selectGroup,
  sendMessage,
  sendGroupMessage,
  handleTyping,
  disconnect
} = useChat();

// 使用群组相关的composables
const {
  availableGroups,
  userGroups,
  showCreateGroupModal,
  newGroupName,
  newGroupDescription,
  createGroup,
  joinGroup,
  leaveGroup,
  resetCreateGroupForm,
  setupGroupEventListeners
} = useGroups(currentUser.value);

// 组件挂载时设置群组事件监听器
onMounted(() => {
  setupGroupEventListeners();
});
</script>

<style scoped>
.chat-container {
  width: 100%;
  max-width: 1200px;
  height: 90vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-content {
  flex: 1;
  display: flex;
  height: calc(100% - 72px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
  }
  
  .chat-content {
    flex-direction: column;
  }
}
</style>
