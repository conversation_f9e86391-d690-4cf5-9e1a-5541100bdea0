from flask import Flask, request
from flask_socketio import <PERSON>cket<PERSON>, emit
from flask_cors import CORS
import uuid

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
CORS(app, origins="*")
socketio = SocketIO(app, cors_allowed_origins="*")

# 存储在线用户信息
online_users = {}
# 存储用户的socket ID映射
user_sessions = {}
# 存储群组信息
groups = {}
# 存储用户所在的群组
user_groups = {}

@app.route('/')
def index():
    return {"message": "Flask SocketIO Server is running"}

@socketio.on('connect')
def on_connect():
    print(f'Client connected: {request.sid}')
    emit('connected', {'data': 'Connected to server'})

@socketio.on('disconnect')
def on_disconnect():
    print(f'Client disconnected: {request.sid}')
    # 从在线用户列表中移除用户
    user_to_remove = None
    for user_id, session_id in user_sessions.items():
        if session_id == request.sid:
            user_to_remove = user_id
            break
    
    if user_to_remove:
        # 从所有群组中移除用户
        if user_to_remove in user_groups:
            for group_id in user_groups[user_to_remove][:]:  # 使用切片复制避免修改时出错
                if group_id in groups:
                    groups[group_id]['members'].remove(user_to_remove)
                    # 如果群组没有成员了，删除群组
                    if not groups[group_id]['members']:
                        del groups[group_id]
                        emit('group_deleted', {'group_id': group_id}, broadcast=True)
                    else:
                        # 通知群组内其他成员有用户离开
                        for member_id in groups[group_id]['members']:
                            if member_id in user_sessions:
                                socketio.emit('user_left_group', {
                                    'group_id': group_id,
                                    'group_name': groups[group_id]['group_name'],
                                    'user_id': user_to_remove,
                                    'username': online_users.get(user_to_remove, {}).get('username', 'Unknown')
                                }, room=user_sessions[member_id])
            del user_groups[user_to_remove]

        if user_to_remove in online_users:
            del online_users[user_to_remove]
        del user_sessions[user_to_remove]
        # 通知所有客户端更新用户列表和群组列表
        emit('user_list_updated', {'users': list(online_users.values())}, broadcast=True)
        emit('group_list_updated', {'groups': list(groups.values())}, broadcast=True)
        emit('user_disconnected', {'user_id': user_to_remove}, broadcast=True)

@socketio.on('user_join')
def on_user_join(data):
    user_id = data.get('user_id')
    username = data.get('username')
    
    if not user_id or not username:
        emit('error', {'message': 'User ID and username are required'})
        return
    
    # 如果用户已经在线，先断开之前的连接
    if user_id in user_sessions:
        old_session_id = user_sessions[user_id]
        socketio.disconnect(old_session_id)
    
    # 添加用户到在线列表
    online_users[user_id] = {
        'user_id': user_id,
        'username': username,
        'session_id': request.sid
    }
    user_sessions[user_id] = request.sid
    
    # 发送当前用户信息给客户端
    emit('user_joined', {
        'user_id': user_id,
        'username': username,
        'users': list(online_users.values())
    })
    
    # 通知所有其他客户端有新用户加入
    emit('user_list_updated', {'users': list(online_users.values())}, broadcast=True)
    emit('new_user_joined', {
        'user_id': user_id,
        'username': username
    }, broadcast=True, include_self=False)

@socketio.on('send_private_message')
def on_send_private_message(data):
    sender_id = data.get('sender_id')
    recipient_id = data.get('recipient_id')
    message = data.get('message')
    timestamp = data.get('timestamp')
    
    if not all([sender_id, recipient_id, message]):
        emit('error', {'message': 'Sender ID, recipient ID, and message are required'})
        return
    
    # 检查发送者是否在线
    if sender_id not in online_users:
        emit('error', {'message': 'Sender not found'})
        return
    
    # 检查接收者是否在线
    if recipient_id not in online_users:
        emit('message_status', {
            'message_id': data.get('message_id'),
            'status': 'failed',
            'error': 'Recipient is offline'
        })
        return
    
    # 获取接收者的session ID
    recipient_session_id = user_sessions.get(recipient_id)
    
    if not recipient_session_id:
        emit('message_status', {
            'message_id': data.get('message_id'),
            'status': 'failed',
            'error': 'Recipient session not found'
        })
        return
    
    # 构造消息数据
    message_data = {
        'message_id': data.get('message_id', str(uuid.uuid4())),
        'sender_id': sender_id,
        'sender_username': online_users[sender_id]['username'],
        'recipient_id': recipient_id,
        'message': message,
        'timestamp': timestamp,
        'type': 'private'
    }
    
    # 发送消息给接收者
    socketio.emit('receive_private_message', message_data, room=recipient_session_id)
    
    # 发送确认给发送者
    emit('message_status', {
        'message_id': message_data['message_id'],
        'status': 'delivered'
    })

@socketio.on('get_online_users')
def on_get_online_users():
    emit('user_list_updated', {'users': list(online_users.values())})

@socketio.on('typing_start')
def on_typing_start(data):
    sender_id = data.get('sender_id')
    recipient_id = data.get('recipient_id')
    
    if recipient_id in user_sessions:
        recipient_session_id = user_sessions[recipient_id]
        socketio.emit('user_typing', {
            'user_id': sender_id,
            'username': online_users.get(sender_id, {}).get('username', 'Unknown'),
            'typing': True
        }, room=recipient_session_id)

@socketio.on('typing_stop')
def on_typing_stop(data):
    sender_id = data.get('sender_id')
    recipient_id = data.get('recipient_id')
    
    if recipient_id in user_sessions:
        recipient_session_id = user_sessions[recipient_id]
        socketio.emit('user_typing', {
            'user_id': sender_id,
            'username': online_users.get(sender_id, {}).get('username', 'Unknown'),
            'typing': False
        }, room=recipient_session_id)

@socketio.on('create_group')
def on_create_group(data):
    creator_id = data.get('creator_id')
    group_name = data.get('group_name')
    group_description = data.get('group_description', '')

    if not creator_id or not group_name:
        emit('error', {'message': 'Creator ID and group name are required'})
        return

    if creator_id not in online_users:
        emit('error', {'message': 'Creator not found'})
        return

    # 生成群组ID
    group_id = f"group_{len(groups) + 1}_{creator_id}"

    # 创建群组
    groups[group_id] = {
        'group_id': group_id,
        'group_name': group_name,
        'description': group_description,
        'creator_id': creator_id,
        'creator_username': online_users[creator_id]['username'],
        'members': [creator_id],
        'created_at': str(uuid.uuid4())  # 简单的时间戳替代
    }

    # 将创建者加入群组
    if creator_id not in user_groups:
        user_groups[creator_id] = []
    user_groups[creator_id].append(group_id)

    # 通知创建者群组创建成功
    emit('group_created', {
        'group': groups[group_id],
        'message': f'群组 "{group_name}" 创建成功'
    })

    # 通知所有在线用户有新群组
    emit('group_list_updated', {'groups': list(groups.values())}, broadcast=True)

@socketio.on('join_group')
def on_join_group(data):
    user_id = data.get('user_id')
    group_id = data.get('group_id')

    if not user_id or not group_id:
        emit('error', {'message': 'User ID and group ID are required'})
        return

    if user_id not in online_users:
        emit('error', {'message': 'User not found'})
        return

    if group_id not in groups:
        emit('error', {'message': 'Group not found'})
        return

    # 检查用户是否已经在群组中
    if user_id in groups[group_id]['members']:
        emit('error', {'message': 'User already in group'})
        return

    # 将用户加入群组
    groups[group_id]['members'].append(user_id)

    if user_id not in user_groups:
        user_groups[user_id] = []
    user_groups[user_id].append(group_id)

    # 通知用户加入成功
    emit('group_joined', {
        'group': groups[group_id],
        'message': f'成功加入群组 "{groups[group_id]["group_name"]}"'
    })

    # 通知群组内所有成员有新用户加入
    for member_id in groups[group_id]['members']:
        if member_id in user_sessions and member_id != user_id:
            socketio.emit('user_joined_group', {
                'group_id': group_id,
                'group_name': groups[group_id]['group_name'],
                'user_id': user_id,
                'username': online_users[user_id]['username']
            }, room=user_sessions[member_id])

    # 更新群组列表
    emit('group_list_updated', {'groups': list(groups.values())}, broadcast=True)

@socketio.on('leave_group')
def on_leave_group(data):
    user_id = data.get('user_id')
    group_id = data.get('group_id')

    if not user_id or not group_id:
        emit('error', {'message': 'User ID and group ID are required'})
        return

    if group_id not in groups:
        emit('error', {'message': 'Group not found'})
        return

    if user_id not in groups[group_id]['members']:
        emit('error', {'message': 'User not in group'})
        return

    # 从群组中移除用户
    groups[group_id]['members'].remove(user_id)

    if user_id in user_groups:
        user_groups[user_id].remove(group_id)

    # 如果群组没有成员了，删除群组
    if not groups[group_id]['members']:
        del groups[group_id]
        emit('group_deleted', {'group_id': group_id}, broadcast=True)
    else:
        # 通知群组内其他成员有用户离开
        for member_id in groups[group_id]['members']:
            if member_id in user_sessions:
                socketio.emit('user_left_group', {
                    'group_id': group_id,
                    'group_name': groups[group_id]['group_name'],
                    'user_id': user_id,
                    'username': online_users.get(user_id, {}).get('username', 'Unknown')
                }, room=user_sessions[member_id])

    # 通知用户离开成功
    emit('group_left', {
        'group_id': group_id,
        'message': f'已离开群组'
    })

    # 更新群组列表
    emit('group_list_updated', {'groups': list(groups.values())}, broadcast=True)

@socketio.on('send_group_message')
def on_send_group_message(data):
    sender_id = data.get('sender_id')
    group_id = data.get('group_id')
    message = data.get('message')
    timestamp = data.get('timestamp')

    if not all([sender_id, group_id, message]):
        emit('error', {'message': 'Sender ID, group ID, and message are required'})
        return

    if sender_id not in online_users:
        emit('error', {'message': 'Sender not found'})
        return

    if group_id not in groups:
        emit('error', {'message': 'Group not found'})
        return

    if sender_id not in groups[group_id]['members']:
        emit('error', {'message': 'Sender not in group'})
        return

    # 构造群组消息数据
    message_data = {
        'message_id': data.get('message_id', str(uuid.uuid4())),
        'sender_id': sender_id,
        'sender_username': online_users[sender_id]['username'],
        'group_id': group_id,
        'group_name': groups[group_id]['group_name'],
        'message': message,
        'timestamp': timestamp,
        'type': 'group'
    }

    # 发送消息给群组内所有成员（除了发送者）
    for member_id in groups[group_id]['members']:
        if member_id in user_sessions and member_id != sender_id:
            socketio.emit('receive_group_message', message_data, room=user_sessions[member_id])

    # 发送确认给发送者
    emit('message_status', {
        'message_id': message_data['message_id'],
        'status': 'delivered',
        'type': 'group'
    })

@socketio.on('get_groups')
def on_get_groups():
    emit('group_list_updated', {'groups': list(groups.values())})

@socketio.on('get_user_groups')
def on_get_user_groups(data):
    user_id = data.get('user_id')
    if user_id and user_id in user_groups:
        user_group_list = []
        for group_id in user_groups[user_id]:
            if group_id in groups:
                user_group_list.append(groups[group_id])
        emit('user_groups_updated', {'groups': user_group_list})
    else:
        emit('user_groups_updated', {'groups': []})

if __name__ == '__main__':
    print("Starting Flask SocketIO server...")
    print("Server will be available at http://localhost:5000")
    socketio.run(app, debug=True, host='0.0.0.0', port=5000)
