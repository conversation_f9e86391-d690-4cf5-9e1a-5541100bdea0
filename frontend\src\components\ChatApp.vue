<template>
  <div class="chat-container">
    <!-- 登录界面 -->
    <div v-if="!isConnected" class="login-container">
      <div class="login-form">
        <h2>加入聊天</h2>
        <input
          v-model="username"
          type="text"
          placeholder="请输入用户名"
          @keyup.enter="joinChat"
          class="username-input"
        />
        <button @click="joinChat" :disabled="!username.trim()" class="join-btn">
          加入聊天
        </button>
      </div>
    </div>

    <!-- 聊天界面 -->
    <div v-else class="chat-interface">
      <!-- 头部 -->
      <div class="chat-header">
        <h3>WebSocket 实时聊天系统</h3>
        <div class="user-info">
          <span>当前用户: {{ currentUser.username }}</span>
          <button @click="disconnect" class="disconnect-btn">断开连接</button>
        </div>
      </div>

      <div class="chat-content">
        <!-- 侧边栏 -->
        <div class="sidebar">
          <!-- 切换标签 -->
          <div class="sidebar-tabs">
            <button
              :class="['tab-btn', { active: activeTab === 'users' }]"
              @click="activeTab = 'users'"
            >
              用户 ({{ onlineUsers.length }})
            </button>
            <button
              :class="['tab-btn', { active: activeTab === 'groups' }]"
              @click="activeTab = 'groups'"
            >
              群组 ({{ availableGroups.length }})
            </button>
          </div>

          <!-- 在线用户列表 -->
          <div v-show="activeTab === 'users'" class="users-panel">
            <div class="users-list">
              <div
                v-for="user in onlineUsers"
                :key="user.user_id"
                :class="[
                  'user-item',
                  {
                    'current-user': user.user_id === currentUser.user_id,
                    selected:
                      chatMode === 'private' &&
                      selectedUser?.user_id === user.user_id,
                  },
                ]"
                @click="selectUser(user)"
              >
                <div class="user-avatar">
                  {{ user.username.charAt(0).toUpperCase() }}
                </div>
                <span class="user-name">{{ user.username }}</span>
                <span
                  v-if="user.user_id === currentUser.user_id"
                  class="current-label"
                  >(你)</span
                >
              </div>
            </div>
          </div>

          <!-- 群组列表 -->
          <div v-show="activeTab === 'groups'" class="groups-panel">
            <div class="group-actions">
              <button
                @click="showCreateGroupModal = true"
                class="create-group-btn"
              >
                创建群组
              </button>
            </div>
            <div class="groups-list">
              <div
                v-for="group in availableGroups"
                :key="group.group_id"
                :class="[
                  'group-item',
                  {
                    selected:
                      chatMode === 'group' &&
                      selectedGroup?.group_id === group.group_id,
                    joined: userGroups.some(
                      (g) => g.group_id === group.group_id
                    ),
                  },
                ]"
                @click="selectGroup(group)"
              >
                <div class="group-avatar">#</div>
                <div class="group-info">
                  <span class="group-name">{{ group.group_name }}</span>
                  <span class="group-members"
                    >{{ group.members.length }} 成员</span
                  >
                </div>
                <button
                  v-if="!userGroups.some((g) => g.group_id === group.group_id)"
                  @click.stop="joinGroup(group.group_id)"
                  class="join-group-btn"
                >
                  加入
                </button>
                <button
                  v-else
                  @click.stop="leaveGroup(group.group_id)"
                  class="leave-group-btn"
                >
                  离开
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 聊天区域 -->
        <div class="chat-area">
          <div v-if="!selectedUser && !selectedGroup" class="no-chat-selected">
            <p>请选择一个用户或群组开始聊天</p>
          </div>

          <!-- 私聊窗口 -->
          <div
            v-else-if="chatMode === 'private' && selectedUser"
            class="chat-window"
          >
            <!-- 聊天头部 -->
            <div class="chat-window-header">
              <h4>与 {{ selectedUser.username }} 的对话</h4>
              <div
                v-if="typingUsers[selectedUser.user_id]"
                class="typing-indicator"
              >
                {{ selectedUser.username }} 正在输入...
              </div>
            </div>

            <!-- 消息列表 -->
            <div class="messages-container" ref="messagesContainer">
              <div
                v-for="message in getMessagesForUser(selectedUser.user_id)"
                :key="message.message_id"
                :class="[
                  'message',
                  {
                    'own-message': message.sender_id === currentUser.user_id,
                    'other-message': message.sender_id !== currentUser.user_id,
                  },
                ]"
              >
                <div class="message-content">
                  <p>{{ message.message }}</p>
                  <span class="message-time">{{
                    formatTime(message.timestamp)
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
              <input
                v-model="messageInput"
                type="text"
                placeholder="输入消息..."
                @keyup.enter="sendMessage"
                @input="handleTyping"
                class="message-input"
              />
              <button
                @click="sendMessage"
                :disabled="!messageInput.trim()"
                class="send-btn"
              >
                发送
              </button>
            </div>
          </div>

          <!-- 群聊窗口 -->
          <div
            v-else-if="chatMode === 'group' && selectedGroup"
            class="chat-window"
          >
            <!-- 聊天头部 -->
            <div class="chat-window-header">
              <h4># {{ selectedGroup.group_name }}</h4>
              <div class="group-info-header">
                <span>{{ selectedGroup.members.length }} 成员</span>
                <span class="group-description">{{
                  selectedGroup.description
                }}</span>
              </div>
            </div>

            <!-- 消息列表 -->
            <div class="messages-container" ref="messagesContainer">
              <div
                v-for="message in getGroupMessages(selectedGroup.group_id)"
                :key="message.message_id"
                :class="[
                  'message',
                  {
                    'own-message': message.sender_id === currentUser.user_id,
                    'other-message': message.sender_id !== currentUser.user_id,
                  },
                ]"
              >
                <div class="message-content">
                  <div
                    v-if="message.sender_id !== currentUser.user_id"
                    class="message-sender"
                  >
                    {{ message.sender_username }}
                  </div>
                  <p>{{ message.message }}</p>
                  <span class="message-time">{{
                    formatTime(message.timestamp)
                  }}</span>
                </div>
              </div>
            </div>

            <!-- 输入区域 -->
            <div class="input-area">
              <input
                v-model="messageInput"
                type="text"
                placeholder="输入群组消息..."
                @keyup.enter="sendGroupMessage"
                class="message-input"
              />
              <button
                @click="sendGroupMessage"
                :disabled="!messageInput.trim()"
                class="send-btn"
              >
                发送
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 创建群组模态框 -->
    <div
      v-if="showCreateGroupModal"
      class="modal-overlay"
      @click="showCreateGroupModal = false"
    >
      <div class="modal-content" @click.stop>
        <h3>创建群组</h3>
        <div class="form-group">
          <label>群组名称</label>
          <input
            v-model="newGroupName"
            type="text"
            placeholder="请输入群组名称"
            class="form-input"
          />
        </div>
        <div class="form-group">
          <label>群组描述</label>
          <textarea
            v-model="newGroupDescription"
            placeholder="请输入群组描述（可选）"
            class="form-textarea"
          ></textarea>
        </div>
        <div class="modal-actions">
          <button @click="showCreateGroupModal = false" class="cancel-btn">
            取消
          </button>
          <button
            @click="createGroup"
            :disabled="!newGroupName.trim()"
            class="confirm-btn"
          >
            创建
          </button>
        </div>
      </div>
    </div>

    <!-- 连接状态指示器 -->
    <div class="connection-status" :class="connectionStatus">
      {{ connectionStatusText }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from "vue";
import { io, Socket } from "socket.io-client";

// 类型定义
interface User {
  user_id: string;
  username: string;
  session_id?: string;
}

interface Message {
  message_id: string;
  sender_id: string;
  sender_username: string;
  recipient_id?: string;
  group_id?: string;
  group_name?: string;
  message: string;
  timestamp: string;
  type: "private" | "group";
}

interface Group {
  group_id: string;
  group_name: string;
  description: string;
  creator_id: string;
  creator_username: string;
  members: string[];
  created_at: string;
}

// 响应式数据
const socket = ref<Socket | null>(null);
const isConnected = ref(false);
const username = ref("");
const currentUser = ref<User>({ user_id: "", username: "" });
const onlineUsers = ref<User[]>([]);
const selectedUser = ref<User | null>(null);
const selectedGroup = ref<Group | null>(null);
const availableGroups = ref<Group[]>([]);
const userGroups = ref<Group[]>([]);
const messages = reactive<{ [userId: string]: Message[] }>({});
const groupMessages = reactive<{ [groupId: string]: Message[] }>({});
const messageInput = ref("");
const typingUsers = reactive<{ [userId: string]: boolean }>({});
const connectionStatus = ref("disconnected");
const connectionStatusText = ref("未连接");
const messagesContainer = ref<HTMLElement>();
const chatMode = ref<"private" | "group">("private");
const activeTab = ref<"users" | "groups">("users");
const showCreateGroupModal = ref(false);
const newGroupName = ref("");
const newGroupDescription = ref("");

// 输入防抖
let typingTimeout: number | null = null;

// 连接状态监听
watch(isConnected, (connected) => {
  if (connected) {
    connectionStatus.value = "connected";
    connectionStatusText.value = "已连接";
  } else {
    connectionStatus.value = "disconnected";
    connectionStatusText.value = "未连接";
  }
});

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 加入聊天
const joinChat = () => {
  if (!username.value.trim()) return;

  const userId = generateId();
  currentUser.value = {
    user_id: userId,
    username: username.value.trim(),
  };

  // 连接到服务器
  socket.value = io("http://localhost:5000");

  setupSocketListeners();

  // 发送用户加入事件
  socket.value.emit("user_join", {
    user_id: currentUser.value.user_id,
    username: currentUser.value.username,
  });
};

// 设置Socket监听器
const setupSocketListeners = () => {
  if (!socket.value) return;

  socket.value.on("connect", () => {
    console.log("Connected to server");
    connectionStatus.value = "connected";
    connectionStatusText.value = "已连接";
  });

  socket.value.on("disconnect", () => {
    console.log("Disconnected from server");
    isConnected.value = false;
    connectionStatus.value = "disconnected";
    connectionStatusText.value = "连接断开";
  });

  socket.value.on("user_joined", (data) => {
    console.log("User joined:", data);
    isConnected.value = true;
    onlineUsers.value = data.users || [];
    // 获取群组列表
    socket.value?.emit("get_groups");
    socket.value?.emit("get_user_groups", {
      user_id: currentUser.value.user_id,
    });
  });

  socket.value.on("user_list_updated", (data) => {
    console.log("User list updated:", data);
    onlineUsers.value = data.users || [];
  });

  socket.value.on("new_user_joined", (data) => {
    console.log("New user joined:", data);
  });

  socket.value.on("user_disconnected", (data) => {
    console.log("User disconnected:", data);
  });

  socket.value.on("receive_private_message", (data: Message) => {
    console.log("Received message:", data);
    addMessage(data);
  });

  socket.value.on("message_status", (data) => {
    console.log("Message status:", data);
  });

  socket.value.on("user_typing", (data) => {
    if (data.user_id !== currentUser.value.user_id) {
      typingUsers[data.user_id] = data.typing;
      if (data.typing) {
        window.setTimeout(() => {
          typingUsers[data.user_id] = false;
        }, 3000);
      }
    }
  });

  // 群组相关事件
  socket.value.on("group_created", (data) => {
    console.log("Group created:", data);
    newGroupName.value = "";
    newGroupDescription.value = "";
    showCreateGroupModal.value = false;
  });

  socket.value.on("group_joined", (data) => {
    console.log("Group joined:", data);
  });

  socket.value.on("group_left", (data) => {
    console.log("Group left:", data);
  });

  socket.value.on("group_list_updated", (data) => {
    console.log("Group list updated:", data);
    availableGroups.value = data.groups || [];
  });

  socket.value.on("user_groups_updated", (data) => {
    console.log("User groups updated:", data);
    userGroups.value = data.groups || [];
  });

  socket.value.on("receive_group_message", (data) => {
    console.log("Received group message:", data);
    addGroupMessage(data);
  });

  socket.value.on("user_joined_group", (data) => {
    console.log("User joined group:", data);
  });

  socket.value.on("user_left_group", (data) => {
    console.log("User left group:", data);
  });

  socket.value.on("group_deleted", (data) => {
    console.log("Group deleted:", data);
    if (selectedGroup.value?.group_id === data.group_id) {
      selectedGroup.value = null;
      chatMode.value = "private";
    }
  });

  socket.value.on("error", (error) => {
    console.error("Socket error:", error);
    connectionStatus.value = "error";
    connectionStatusText.value = "连接错误";
  });
};

// 选择用户
const selectUser = (user: User) => {
  if (user.user_id === currentUser.value.user_id) return;
  selectedUser.value = user;
  selectedGroup.value = null;
  chatMode.value = "private";
  nextTick(() => {
    scrollToBottom();
  });
};

// 选择群组
const selectGroup = (group: Group) => {
  selectedGroup.value = group;
  selectedUser.value = null;
  chatMode.value = "group";
  nextTick(() => {
    scrollToBottom();
  });
};

// 发送消息
const sendMessage = () => {
  if (!messageInput.value.trim() || !selectedUser.value || !socket.value)
    return;

  const message: Message = {
    message_id: generateId(),
    sender_id: currentUser.value.user_id,
    sender_username: currentUser.value.username,
    recipient_id: selectedUser.value.user_id,
    message: messageInput.value.trim(),
    timestamp: new Date().toISOString(),
    type: "private",
  };

  // 发送到服务器
  socket.value.emit("send_private_message", message);

  // 添加到本地消息列表
  addMessage(message);

  messageInput.value = "";

  nextTick(() => {
    scrollToBottom();
  });
};

// 添加消息到列表
const addMessage = (message: Message) => {
  const otherUserId =
    message.sender_id === currentUser.value.user_id
      ? message.recipient_id
      : message.sender_id;

  if (!messages[otherUserId]) {
    messages[otherUserId] = [];
  }

  messages[otherUserId].push(message);
};

// 获取与指定用户的消息
const getMessagesForUser = (userId: string) => {
  return messages[userId] || [];
};

// 获取群组消息
const getGroupMessages = (groupId: string) => {
  return groupMessages[groupId] || [];
};

// 添加群组消息到列表
const addGroupMessage = (message: Message) => {
  if (!message.group_id) return;

  if (!groupMessages[message.group_id]) {
    groupMessages[message.group_id] = [];
  }

  groupMessages[message.group_id].push(message);

  nextTick(() => {
    scrollToBottom();
  });
};

// 发送群组消息
const sendGroupMessage = () => {
  if (!messageInput.value.trim() || !selectedGroup.value || !socket.value)
    return;

  const message: Message = {
    message_id: generateId(),
    sender_id: currentUser.value.user_id,
    sender_username: currentUser.value.username,
    group_id: selectedGroup.value.group_id,
    group_name: selectedGroup.value.group_name,
    message: messageInput.value.trim(),
    timestamp: new Date().toISOString(),
    type: "group",
  };

  // 发送到服务器
  socket.value.emit("send_group_message", message);

  // 添加到本地消息列表
  addGroupMessage(message);

  messageInput.value = "";
};

// 创建群组
const createGroup = () => {
  if (!newGroupName.value.trim() || !socket.value) return;

  socket.value.emit("create_group", {
    creator_id: currentUser.value.user_id,
    group_name: newGroupName.value.trim(),
    group_description: newGroupDescription.value.trim(),
  });
};

// 加入群组
const joinGroup = (groupId: string) => {
  if (!socket.value) return;

  socket.value.emit("join_group", {
    user_id: currentUser.value.user_id,
    group_id: groupId,
  });
};

// 离开群组
const leaveGroup = (groupId: string) => {
  if (!socket.value) return;

  socket.value.emit("leave_group", {
    user_id: currentUser.value.user_id,
    group_id: groupId,
  });

  // 如果当前选中的是这个群组，清除选择
  if (selectedGroup.value?.group_id === groupId) {
    selectedGroup.value = null;
    chatMode.value = "private";
  }
};

// 处理输入事件（打字指示器）
const handleTyping = () => {
  if (!selectedUser.value || !socket.value) return;

  // 发送开始打字事件
  socket.value.emit("typing_start", {
    sender_id: currentUser.value.user_id,
    recipient_id: selectedUser.value.user_id,
  });

  // 清除之前的定时器
  if (typingTimeout) {
    clearTimeout(typingTimeout);
  }

  // 设置新的定时器，1秒后发送停止打字事件
  typingTimeout = window.setTimeout(() => {
    if (socket.value && selectedUser.value) {
      socket.value.emit("typing_stop", {
        sender_id: currentUser.value.user_id,
        recipient_id: selectedUser.value.user_id,
      });
    }
  }, 1000);
};

// 格式化时间
const formatTime = (timestamp: string) => {
  const date = new Date(timestamp);
  return date.toLocaleTimeString("zh-CN", {
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 滚动到底部
const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 断开连接
const disconnect = () => {
  if (socket.value) {
    socket.value.disconnect();
  }
  isConnected.value = false;
  currentUser.value = { user_id: "", username: "" };
  onlineUsers.value = [];
  selectedUser.value = null;
  selectedGroup.value = null;
  availableGroups.value = [];
  userGroups.value = [];
  Object.keys(messages).forEach((key) => delete messages[key]);
  Object.keys(groupMessages).forEach((key) => delete groupMessages[key]);
  chatMode.value = "private";
  activeTab.value = "users";
  username.value = "";
};

// 组件卸载时清理
onUnmounted(() => {
  if (socket.value) {
    socket.value.disconnect();
  }
  if (typingTimeout) {
    clearTimeout(typingTimeout);
  }
});
</script>

<style scoped>
.chat-container {
  width: 100%;
  max-width: 1200px;
  height: 90vh;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

/* 登录界面样式 */
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 300px;
}

.login-form h2 {
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
}

.username-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 20px;
  transition: border-color 0.3s;
}

.username-input:focus {
  outline: none;
  border-color: #667eea;
}

.join-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: transform 0.2s;
}

.join-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.join-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 聊天界面样式 */
.chat-interface {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.disconnect-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.disconnect-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.chat-content {
  flex: 1;
  display: flex;
  height: calc(100% - 72px);
}

/* 侧边栏样式 */
.sidebar {
  width: 320px;
  background: #f8f9fa;
  border-right: 1px solid #e1e5e9;
  display: flex;
  flex-direction: column;
}

.sidebar-tabs {
  display: flex;
  background: #e9ecef;
  border-bottom: 1px solid #dee2e6;
}

.tab-btn {
  flex: 1;
  padding: 12px 16px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: #6c757d;
  transition: all 0.3s;
}

.tab-btn.active {
  background: #f8f9fa;
  color: #495057;
  border-bottom: 2px solid #667eea;
}

.tab-btn:hover {
  background: #f1f3f4;
}

/* 用户列表样式 */
.users-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.users-panel h4 {
  padding: 16px 20px;
  margin: 0;
  background: #e9ecef;
  color: #495057;
  font-size: 14px;
  font-weight: 600;
  border-bottom: 1px solid #dee2e6;
}

.users-list {
  flex: 1;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #e9ecef;
}

.user-item:hover {
  background: #e9ecef;
}

.user-item.selected {
  background: #667eea;
  color: white;
}

.user-item.current-user {
  background: #f1f3f4;
  cursor: default;
}

.user-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  font-size: 14px;
}

.user-item.selected .user-avatar {
  background: rgba(255, 255, 255, 0.2);
}

.user-name {
  flex: 1;
  font-weight: 500;
}

.current-label {
  font-size: 12px;
  opacity: 0.7;
}

/* 群组列表样式 */
.groups-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.group-actions {
  padding: 16px 20px;
  border-bottom: 1px solid #dee2e6;
}

.create-group-btn {
  width: 100%;
  padding: 8px 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: transform 0.2s;
}

.create-group-btn:hover {
  transform: translateY(-1px);
}

.groups-list {
  flex: 1;
  overflow-y: auto;
}

.group-item {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #e9ecef;
}

.group-item:hover {
  background: #e9ecef;
}

.group-item.selected {
  background: #667eea;
  color: white;
}

.group-item.joined {
  background: #e8f5e8;
}

.group-item.joined.selected {
  background: #667eea;
  color: white;
}

.group-avatar {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 12px;
  font-size: 18px;
}

.group-item.selected .group-avatar {
  background: rgba(255, 255, 255, 0.2);
}

.group-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.group-name {
  font-weight: 500;
  margin-bottom: 2px;
}

.group-members {
  font-size: 12px;
  opacity: 0.7;
}

.join-group-btn,
.leave-group-btn {
  padding: 4px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.join-group-btn {
  background: #28a745;
  color: white;
}

.join-group-btn:hover {
  background: #218838;
}

.leave-group-btn {
  background: #dc3545;
  color: white;
}

.leave-group-btn:hover {
  background: #c82333;
}

/* 聊天区域样式 */
.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.no-chat-selected {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #6c757d;
  font-size: 18px;
}

.chat-window {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chat-window-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e1e5e9;
  background: #f8f9fa;
}

.chat-window-header h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 16px;
}

.typing-indicator {
  font-size: 12px;
  color: #667eea;
  font-style: italic;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  background: #fff;
}

.message {
  margin-bottom: 16px;
  display: flex;
}

.message.own-message {
  justify-content: flex-end;
}

.message.other-message {
  justify-content: flex-start;
}

.message-content {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  position: relative;
}

.own-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.other-message .message-content {
  background: #f1f3f4;
  color: #333;
}

.message-content p {
  margin: 0 0 4px 0;
  word-wrap: break-word;
}

.message-time {
  font-size: 11px;
  opacity: 0.7;
}

.message-sender {
  font-size: 12px;
  font-weight: 600;
  color: #667eea;
  margin-bottom: 4px;
}

.group-info-header {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  font-size: 12px;
}

.group-description {
  opacity: 0.8;
  margin-top: 2px;
}

.input-area {
  padding: 16px 24px;
  border-top: 1px solid #e1e5e9;
  background: #f8f9fa;
  display: flex;
  gap: 12px;
}

.message-input {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #e1e5e9;
  border-radius: 24px;
  font-size: 14px;
  outline: none;
  transition: border-color 0.3s;
}

.message-input:focus {
  border-color: #667eea;
}

.send-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 24px;
  cursor: pointer;
  font-weight: 500;
  transition: transform 0.2s;
}

.send-btn:hover:not(:disabled) {
  transform: translateY(-1px);
}

.send-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 连接状态指示器 */
.connection-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
}

.connection-status.connected {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.connection-status.disconnected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.connection-status.error {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 24px;
  min-width: 400px;
  max-width: 500px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.modal-content h3 {
  margin: 0 0 20px 0;
  color: #333;
  font-size: 20px;
}

.form-group {
  margin-bottom: 16px;
}

.form-group label {
  display: block;
  margin-bottom: 6px;
  font-weight: 500;
  color: #555;
}

.form-input,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.3s;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: #667eea;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 20px;
}

.cancel-btn,
.confirm-btn {
  padding: 10px 20px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-btn {
  background: #f8f9fa;
  color: #6c757d;
  border: 1px solid #dee2e6;
}

.cancel-btn:hover {
  background: #e9ecef;
}

.confirm-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.confirm-btn:hover:not(:disabled) {
  transform: translateY(-1px);
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-container {
    height: 100vh;
    border-radius: 0;
  }

  .sidebar {
    width: 280px;
  }

  .chat-content {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    height: 250px;
  }

  .chat-area {
    height: calc(100% - 250px);
  }

  .modal-content {
    min-width: 300px;
    margin: 20px;
  }
}
</style>
