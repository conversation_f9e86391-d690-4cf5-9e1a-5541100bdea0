<template>
  <div class="connection-status" :class="status">
    {{ statusText }}
  </div>
</template>

<script setup lang="ts">
import type { ConnectionStatus } from '@/types/chat';

// Props
interface Props {
  status: ConnectionStatus;
  statusText: string;
}

defineProps<Props>();
</script>

<style scoped>
.connection-status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  z-index: 1000;
}

.connection-status.connected {
  background: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.connection-status.disconnected {
  background: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.connection-status.error {
  background: #fff3cd;
  color: #856404;
  border: 1px solid #ffeaa7;
}
</style>
