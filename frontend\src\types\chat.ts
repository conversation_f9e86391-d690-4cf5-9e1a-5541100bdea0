// 用户类型
export interface User {
  user_id: string;
  username: string;
  session_id?: string;
}

// 消息类型
export interface Message {
  message_id: string;
  sender_id: string;
  sender_username: string;
  recipient_id?: string;
  group_id?: string;
  group_name?: string;
  message: string;
  timestamp: string;
  type: "private" | "group";
}

// 群组类型
export interface Group {
  group_id: string;
  group_name: string;
  description: string;
  creator_id: string;
  creator_username: string;
  members: string[];
  created_at: string;
}

// 聊天模式类型
export type ChatMode = "private" | "group";

// 侧边栏标签类型
export type SidebarTab = "users" | "groups";

// 连接状态类型
export type ConnectionStatus = "connected" | "disconnected" | "error";

// Socket事件数据类型
export interface SocketEventData {
  user_joined: {
    user_id: string;
    username: string;
    users: User[];
  };
  user_list_updated: {
    users: User[];
  };
  group_created: {
    group: Group;
    message: string;
  };
  group_joined: {
    group: Group;
    message: string;
  };
  group_left: {
    group_id: string;
    message: string;
  };
  group_list_updated: {
    groups: Group[];
  };
  user_groups_updated: {
    groups: Group[];
  };
  receive_private_message: Message;
  receive_group_message: Message;
  message_status: {
    message_id: string;
    status: string;
    type?: string;
  };
  user_typing: {
    user_id: string;
    username: string;
    typing: boolean;
  };
  error: {
    message: string;
  };
}
