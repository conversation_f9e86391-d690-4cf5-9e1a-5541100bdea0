import { ref, nextTick } from 'vue';
import type { User, Group, ChatMode, SidebarTab } from '@/types/chat';
import { useSocket } from './useSocket';
import { useMessages } from './useMessages';

export function useChat() {
  const {
    isConnected,
    connectionStatus,
    connectionStatusText,
    connect,
    disconnect: socketDisconnect,
    joinChat: socketJoinChat,
    sendPrivateMessage,
    sendGroupMessage,
    sendTypingStart,
    sendTypingStop,
    on,
    off
  } = useSocket();

  const {
    messages,
    groupMessages,
    typingUsers,
    addMessage,
    addGroupMessage,
    getMessagesForUser,
    getGroupMessages,
    createPrivateMessage,
    createGroupMessage,
    setUserTyping,
    clearAllMessages,
    formatTime
  } = useMessages();

  // 用户相关状态
  const username = ref('');
  const currentUser = ref<User>({ user_id: '', username: '' });
  const onlineUsers = ref<User[]>([]);

  // 聊天相关状态
  const selectedUser = ref<User | null>(null);
  const selectedGroup = ref<Group | null>(null);
  const chatMode = ref<ChatMode>('private');
  const activeTab = ref<SidebarTab>('users');
  const messageInput = ref('');

  // 消息容器引用
  const messagesContainer = ref<HTMLElement>();

  // 打字防抖定时器
  let typingTimeout: number | null = null;

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // 滚动到底部
  const scrollToBottom = () => {
    nextTick(() => {
      if (messagesContainer.value) {
        messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
      }
    });
  };

  // 加入聊天
  const joinChat = () => {
    if (!username.value.trim()) return;

    const userId = generateId();
    currentUser.value = {
      user_id: userId,
      username: username.value.trim()
    };

    // 连接到服务器
    connect();
    
    // 设置事件监听器
    setupEventListeners();
    
    // 发送用户加入事件
    socketJoinChat(currentUser.value);
  };

  // 选择用户进行私聊
  const selectUser = (user: User) => {
    if (user.user_id === currentUser.value.user_id) return;
    
    selectedUser.value = user;
    selectedGroup.value = null;
    chatMode.value = 'private';
    scrollToBottom();
  };

  // 选择群组进行群聊
  const selectGroup = (group: Group) => {
    selectedGroup.value = group;
    selectedUser.value = null;
    chatMode.value = 'group';
    scrollToBottom();
  };

  // 发送私聊消息
  const sendMessage = () => {
    if (!messageInput.value.trim() || !selectedUser.value) return;

    const message = createPrivateMessage(
      currentUser.value.user_id,
      currentUser.value.username,
      selectedUser.value.user_id,
      messageInput.value.trim()
    );

    // 发送到服务器
    sendPrivateMessage(message);

    // 添加到本地消息列表
    addMessage(message);

    messageInput.value = '';
    scrollToBottom();
  };

  // 发送群组消息
  const sendGroupMessageHandler = () => {
    if (!messageInput.value.trim() || !selectedGroup.value) return;

    const message = createGroupMessage(
      currentUser.value.user_id,
      currentUser.value.username,
      selectedGroup.value.group_id,
      selectedGroup.value.group_name,
      messageInput.value.trim()
    );

    // 发送到服务器
    sendGroupMessage(message);

    // 添加到本地消息列表
    addGroupMessage(message);

    messageInput.value = '';
    scrollToBottom();
  };

  // 处理打字事件
  const handleTyping = () => {
    if (!selectedUser.value) return;

    // 发送开始打字事件
    sendTypingStart({
      sender_id: currentUser.value.user_id,
      recipient_id: selectedUser.value.user_id
    });

    // 清除之前的定时器
    if (typingTimeout) {
      clearTimeout(typingTimeout);
    }

    // 设置新的定时器，1秒后发送停止打字事件
    typingTimeout = window.setTimeout(() => {
      if (selectedUser.value) {
        sendTypingStop({
          sender_id: currentUser.value.user_id,
          recipient_id: selectedUser.value.user_id
        });
      }
    }, 1000);
  };

  // 断开连接
  const disconnect = () => {
    socketDisconnect();
    
    // 重置所有状态
    currentUser.value = { user_id: '', username: '' };
    onlineUsers.value = [];
    selectedUser.value = null;
    selectedGroup.value = null;
    chatMode.value = 'private';
    activeTab.value = 'users';
    messageInput.value = '';
    username.value = '';
    
    // 清理消息
    clearAllMessages();
    
    // 清理定时器
    if (typingTimeout) {
      clearTimeout(typingTimeout);
      typingTimeout = null;
    }
  };

  // 设置Socket事件监听器
  const setupEventListeners = () => {
    // 用户相关事件
    on('user_joined', (data: any) => {
      console.log('User joined:', data);
      onlineUsers.value = data.users || [];
    });

    on('user_list_updated', (data: any) => {
      console.log('User list updated:', data);
      onlineUsers.value = data.users || [];
    });

    on('new_user_joined', (data: any) => {
      console.log('New user joined:', data);
    });

    on('user_disconnected', (data: any) => {
      console.log('User disconnected:', data);
    });

    // 消息相关事件
    on('receive_private_message', (data: any) => {
      console.log('Received message:', data);
      addMessage(data);
      scrollToBottom();
    });

    on('receive_group_message', (data: any) => {
      console.log('Received group message:', data);
      addGroupMessage(data);
      scrollToBottom();
    });

    on('message_status', (data: any) => {
      console.log('Message status:', data);
    });

    // 打字状态事件
    on('user_typing', (data: any) => {
      if (data.user_id !== currentUser.value.user_id) {
        setUserTyping(data.user_id, data.typing);
      }
    });

    // 错误事件
    on('error', (error: any) => {
      console.error('Socket error:', error);
    });
  };

  return {
    // 状态
    isConnected,
    connectionStatus,
    connectionStatusText,
    username,
    currentUser,
    onlineUsers,
    selectedUser,
    selectedGroup,
    chatMode,
    activeTab,
    messageInput,
    messagesContainer,
    messages,
    groupMessages,
    typingUsers,
    
    // 方法
    joinChat,
    selectUser,
    selectGroup,
    sendMessage,
    sendGroupMessage: sendGroupMessageHandler,
    handleTyping,
    disconnect,
    scrollToBottom,
    getMessagesForUser,
    getGroupMessages,
    formatTime
  };
}
