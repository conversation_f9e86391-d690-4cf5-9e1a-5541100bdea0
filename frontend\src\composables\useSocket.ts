import { ref, onUnmounted } from 'vue';
import { io, Socket } from 'socket.io-client';
import type { User, ConnectionStatus } from '@/types/chat';

export function useSocket() {
  const socket = ref<Socket | null>(null);
  const isConnected = ref(false);
  const connectionStatus = ref<ConnectionStatus>('disconnected');
  const connectionStatusText = ref('未连接');

  // 连接到服务器
  const connect = (serverUrl: string = 'http://localhost:5000') => {
    if (socket.value?.connected) {
      return socket.value;
    }

    socket.value = io(serverUrl);
    
    // 基础连接事件
    socket.value.on('connect', () => {
      console.log('Connected to server');
      isConnected.value = true;
      connectionStatus.value = 'connected';
      connectionStatusText.value = '已连接';
    });

    socket.value.on('disconnect', () => {
      console.log('Disconnected from server');
      isConnected.value = false;
      connectionStatus.value = 'disconnected';
      connectionStatusText.value = '连接断开';
    });

    socket.value.on('connect_error', (error) => {
      console.error('Connection error:', error);
      connectionStatus.value = 'error';
      connectionStatusText.value = '连接错误';
    });

    return socket.value;
  };

  // 断开连接
  const disconnect = () => {
    if (socket.value) {
      socket.value.disconnect();
      socket.value = null;
    }
    isConnected.value = false;
    connectionStatus.value = 'disconnected';
    connectionStatusText.value = '未连接';
  };

  // 加入聊天
  const joinChat = (user: User) => {
    if (!socket.value) return;
    
    socket.value.emit('user_join', {
      user_id: user.user_id,
      username: user.username
    });
  };

  // 发送私聊消息
  const sendPrivateMessage = (messageData: any) => {
    if (!socket.value) return;
    socket.value.emit('send_private_message', messageData);
  };

  // 发送群组消息
  const sendGroupMessage = (messageData: any) => {
    if (!socket.value) return;
    socket.value.emit('send_group_message', messageData);
  };

  // 创建群组
  const createGroup = (groupData: any) => {
    if (!socket.value) return;
    socket.value.emit('create_group', groupData);
  };

  // 加入群组
  const joinGroup = (data: { user_id: string; group_id: string }) => {
    if (!socket.value) return;
    socket.value.emit('join_group', data);
  };

  // 离开群组
  const leaveGroup = (data: { user_id: string; group_id: string }) => {
    if (!socket.value) return;
    socket.value.emit('leave_group', data);
  };

  // 获取群组列表
  const getGroups = () => {
    if (!socket.value) return;
    socket.value.emit('get_groups');
  };

  // 获取用户群组
  const getUserGroups = (userId: string) => {
    if (!socket.value) return;
    socket.value.emit('get_user_groups', { user_id: userId });
  };

  // 发送打字状态
  const sendTypingStart = (data: { sender_id: string; recipient_id: string }) => {
    if (!socket.value) return;
    socket.value.emit('typing_start', data);
  };

  const sendTypingStop = (data: { sender_id: string; recipient_id: string }) => {
    if (!socket.value) return;
    socket.value.emit('typing_stop', data);
  };

  // 监听事件
  const on = (event: string, callback: (...args: any[]) => void) => {
    if (!socket.value) return;
    socket.value.on(event, callback);
  };

  const off = (event: string, callback?: (...args: any[]) => void) => {
    if (!socket.value) return;
    socket.value.off(event, callback);
  };

  // 组件卸载时清理
  onUnmounted(() => {
    disconnect();
  });

  return {
    socket: socket.value,
    isConnected,
    connectionStatus,
    connectionStatusText,
    connect,
    disconnect,
    joinChat,
    sendPrivateMessage,
    sendGroupMessage,
    createGroup,
    joinGroup,
    leaveGroup,
    getGroups,
    getUserGroups,
    sendTypingStart,
    sendTypingStop,
    on,
    off
  };
}
