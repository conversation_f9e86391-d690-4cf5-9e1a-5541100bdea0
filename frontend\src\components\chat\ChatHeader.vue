<template>
  <div class="chat-header">
    <h3>{{ title }}</h3>
    <div class="user-info">
      <span>当前用户: {{ currentUser.username }}</span>
      <button @click="$emit('disconnect')" class="disconnect-btn">
        断开连接
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { User } from '@/types/chat';

// Props
interface Props {
  title?: string;
  currentUser: User;
}

const props = withDefaults(defineProps<Props>(), {
  title: 'WebSocket 实时聊天系统'
});

// Emits
interface Emits {
  (e: 'disconnect'): void;
}

defineEmits<Emits>();
</script>

<style scoped>
.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 20px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.disconnect-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.disconnect-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
