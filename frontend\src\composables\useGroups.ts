import { ref } from 'vue';
import type { Group, User } from '@/types/chat';
import { useSocket } from './useSocket';

export function useGroups(currentUser: User) {
  const {
    createGroup: socketCreateGroup,
    joinGroup: socketJoinGroup,
    leaveGroup: socketLeaveGroup,
    getGroups,
    getUserGroups,
    on,
    off
  } = useSocket();

  // 群组相关状态
  const availableGroups = ref<Group[]>([]);
  const userGroups = ref<Group[]>([]);
  const showCreateGroupModal = ref(false);
  const newGroupName = ref('');
  const newGroupDescription = ref('');

  // 创建群组
  const createGroup = () => {
    if (!newGroupName.value.trim()) return;

    socketCreateGroup({
      creator_id: currentUser.user_id,
      group_name: newGroupName.value.trim(),
      group_description: newGroupDescription.value.trim()
    });
  };

  // 加入群组
  const joinGroup = (groupId: string) => {
    socketJoinGroup({
      user_id: currentUser.user_id,
      group_id: groupId
    });
  };

  // 离开群组
  const leaveGroup = (groupId: string) => {
    socketLeaveGroup({
      user_id: currentUser.user_id,
      group_id: groupId
    });
  };

  // 检查用户是否已加入群组
  const isUserInGroup = (groupId: string) => {
    return userGroups.value.some(group => group.group_id === groupId);
  };

  // 获取群组成员数量
  const getGroupMemberCount = (group: Group) => {
    return group.members.length;
  };

  // 重置创建群组表单
  const resetCreateGroupForm = () => {
    newGroupName.value = '';
    newGroupDescription.value = '';
    showCreateGroupModal.value = false;
  };

  // 刷新群组数据
  const refreshGroups = () => {
    getGroups();
    getUserGroups(currentUser.user_id);
  };

  // 设置群组相关的Socket事件监听器
  const setupGroupEventListeners = () => {
    // 群组创建成功
    on('group_created', (data: any) => {
      console.log('Group created:', data);
      resetCreateGroupForm();
      refreshGroups();
    });

    // 成功加入群组
    on('group_joined', (data: any) => {
      console.log('Group joined:', data);
      refreshGroups();
    });

    // 成功离开群组
    on('group_left', (data: any) => {
      console.log('Group left:', data);
      refreshGroups();
    });

    // 群组列表更新
    on('group_list_updated', (data: any) => {
      console.log('Group list updated:', data);
      availableGroups.value = data.groups || [];
    });

    // 用户群组列表更新
    on('user_groups_updated', (data: any) => {
      console.log('User groups updated:', data);
      userGroups.value = data.groups || [];
    });

    // 有用户加入群组
    on('user_joined_group', (data: any) => {
      console.log('User joined group:', data);
      refreshGroups();
    });

    // 有用户离开群组
    on('user_left_group', (data: any) => {
      console.log('User left group:', data);
      refreshGroups();
    });

    // 群组被删除
    on('group_deleted', (data: any) => {
      console.log('Group deleted:', data);
      refreshGroups();
    });
  };

  // 清理群组数据
  const clearGroupData = () => {
    availableGroups.value = [];
    userGroups.value = [];
    resetCreateGroupForm();
  };

  return {
    // 状态
    availableGroups,
    userGroups,
    showCreateGroupModal,
    newGroupName,
    newGroupDescription,
    
    // 方法
    createGroup,
    joinGroup,
    leaveGroup,
    isUserInGroup,
    getGroupMemberCount,
    resetCreateGroupForm,
    refreshGroups,
    setupGroupEventListeners,
    clearGroupData
  };
}
