import { ref, reactive, nextTick } from 'vue';
import type { Message, User } from '@/types/chat';

export function useMessages() {
  // 私聊消息存储
  const messages = reactive<{ [userId: string]: Message[] }>({});
  
  // 群聊消息存储
  const groupMessages = reactive<{ [groupId: string]: Message[] }>({});
  
  // 打字状态
  const typingUsers = reactive<{ [userId: string]: boolean }>({});

  // 生成唯一ID
  const generateId = () => {
    return Date.now().toString(36) + Math.random().toString(36).substring(2);
  };

  // 格式化时间
  const formatTime = (timestamp: string) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString('zh-CN', { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  // 添加私聊消息
  const addMessage = (message: Message) => {
    const otherUserId = message.sender_id === message.recipient_id 
      ? message.sender_id 
      : (message.recipient_id || message.sender_id);

    if (!messages[otherUserId]) {
      messages[otherUserId] = [];
    }
    
    messages[otherUserId].push(message);
  };

  // 添加群组消息
  const addGroupMessage = (message: Message) => {
    if (!message.group_id) return;
    
    if (!groupMessages[message.group_id]) {
      groupMessages[message.group_id] = [];
    }
    
    groupMessages[message.group_id].push(message);
  };

  // 获取与指定用户的消息
  const getMessagesForUser = (userId: string) => {
    return messages[userId] || [];
  };

  // 获取群组消息
  const getGroupMessages = (groupId: string) => {
    return groupMessages[groupId] || [];
  };

  // 创建私聊消息对象
  const createPrivateMessage = (
    senderId: string,
    senderUsername: string,
    recipientId: string,
    messageText: string
  ): Message => {
    return {
      message_id: generateId(),
      sender_id: senderId,
      sender_username: senderUsername,
      recipient_id: recipientId,
      message: messageText,
      timestamp: new Date().toISOString(),
      type: 'private'
    };
  };

  // 创建群组消息对象
  const createGroupMessage = (
    senderId: string,
    senderUsername: string,
    groupId: string,
    groupName: string,
    messageText: string
  ): Message => {
    return {
      message_id: generateId(),
      sender_id: senderId,
      sender_username: senderUsername,
      group_id: groupId,
      group_name: groupName,
      message: messageText,
      timestamp: new Date().toISOString(),
      type: 'group'
    };
  };

  // 设置用户打字状态
  const setUserTyping = (userId: string, isTyping: boolean) => {
    typingUsers[userId] = isTyping;
    
    if (isTyping) {
      // 3秒后自动清除打字状态
      setTimeout(() => {
        typingUsers[userId] = false;
      }, 3000);
    }
  };

  // 清理所有消息
  const clearAllMessages = () => {
    Object.keys(messages).forEach(key => delete messages[key]);
    Object.keys(groupMessages).forEach(key => delete groupMessages[key]);
    Object.keys(typingUsers).forEach(key => delete typingUsers[key]);
  };

  // 清理指定用户的消息
  const clearUserMessages = (userId: string) => {
    if (messages[userId]) {
      delete messages[userId];
    }
  };

  // 清理指定群组的消息
  const clearGroupMessages = (groupId: string) => {
    if (groupMessages[groupId]) {
      delete groupMessages[groupId];
    }
  };

  return {
    messages,
    groupMessages,
    typingUsers,
    generateId,
    formatTime,
    addMessage,
    addGroupMessage,
    getMessagesForUser,
    getGroupMessages,
    createPrivateMessage,
    createGroupMessage,
    setUserTyping,
    clearAllMessages,
    clearUserMessages,
    clearGroupMessages
  };
}
