<template>
  <div class="login-container">
    <div class="login-form">
      <h2>加入聊天</h2>
      <input
        v-model="username"
        type="text"
        placeholder="请输入用户名"
        @keyup.enter="handleJoinChat"
        class="username-input"
      />
      <button
        @click="handleJoinChat"
        :disabled="!username.trim()"
        class="join-btn"
      >
        加入聊天
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from "vue";

// Props
interface Props {
  modelValue: string;
}

const props = defineProps<Props>();

// Emits
interface Emits {
  (e: "update:modelValue", value: string): void;
  (e: "join-chat"): void;
}

const emit = defineEmits<Emits>();

// 本地状态
const username = ref(props.modelValue);

// 处理加入聊天
const handleJoinChat = () => {
  if (!username.value.trim()) return;

  emit("update:modelValue", username.value);
  emit("join-chat");
};

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    username.value = newValue;
  }
);
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  text-align: center;
  min-width: 300px;
}

.login-form h2 {
  margin-bottom: 30px;
  color: #333;
  font-size: 24px;
}

.username-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 16px;
  margin-bottom: 20px;
  transition: border-color 0.3s;
}

.username-input:focus {
  outline: none;
  border-color: #667eea;
}

.join-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: transform 0.2s;
}

.join-btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.join-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}
</style>
